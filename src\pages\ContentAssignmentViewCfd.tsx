import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Filter,
  FileDown,
  ArrowLeft,
  X,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";

// CFD areas, topics, and units (same as in EmployeeTrainingAssignment)
const cfdAreas = [
  "Patient Safety Checklist",
  "Medication Checklist",
  "Equipment Checklist",
];

const cfdTopics = {
  "Patient Safety Checklist": [
    "Admission Form",
    "Discharge Form",
    "Daily Assessment Form",
  ],
  "Medication Checklist": [
    "Prescription Form",
    "Administration Form",
    "Inventory Form",
  ],
  "Equipment Checklist": [
    "Maintenance Form",
    "Calibration Form",
    "Inspection Form",
  ],
};

const cfdUnits = {
  "Admission Form": [
    "Patient Identification Document",
    "Medical History Document",
    "Initial Assessment Document",
  ],
  "Discharge Form": [
    "Discharge Summary Document",
    "Follow-up Instructions Document",
    "Medication List Document",
  ],
  "Daily Assessment Form": [
    "Vital Signs Document",
    "Pain Assessment Document",
    "Progress Notes Document",
  ],
  "Prescription Form": [
    "Medication Order Document",
    "Dosage Calculation Document",
    "Contraindications Document",
  ],
  "Administration Form": [
    "Administration Schedule Document",
    "Patient Response Document",
    "Side Effects Document",
  ],
  "Inventory Form": [
    "Stock Level Document",
    "Expiry Tracking Document",
    "Reorder Document",
  ],
  "Maintenance Form": [
    "Maintenance Schedule Document",
    "Service History Document",
    "Parts Replacement Document",
  ],
  "Calibration Form": [
    "Calibration Procedure Document",
    "Accuracy Verification Document",
    "Calibration Log Document",
  ],
  "Inspection Form": [
    "Safety Inspection Document",
    "Functionality Test Document",
    "Compliance Document",
  ],
};

const statuses = ["Not Started", "In Progress", "Completed"];

// Mock CFD assignments data (filtered for CFD only)
const mockCfdAssignments = [
  {
    id: "4",
    employeeIds: ["4", "5"],
    employeeName: "2 Employees",
    trainingProgram:
      "Patient Safety Checklist → Admission Form → Patient Identification Document | Patient Safety Checklist → Discharge Form → Discharge Summary Document",
    businessUnit: "KVP",
    departmentGroup: "Clinical Services",
    department: "Nursing",
    division: "Patient Care",
    subDivision: "Intensive Care",
    category: "Clinical",
    grade: "Mid-level",
    designation: "Registered Nurse",
    status: "In Progress",
    assignmentStatus: "Active",
    assignedDate: "2024-01-08",
    dueDate: "2024-02-08",
    notes: "CFD assignment for patient safety documentation.",
  },
  {
    id: "5",
    employeeIds: ["5", "6"],
    employeeName: "2 Employees",
    trainingProgram:
      "Medication Checklist → Prescription Form → Medication Order Document | Medication Checklist → Administration Form → Administration Schedule Document",
    businessUnit: "KTN",
    departmentGroup: "Clinical Services",
    department: "Pharmacy",
    division: "Medication Management",
    subDivision: "Prescription Services",
    category: "Clinical",
    grade: "Senior",
    designation: "Pharmacist",
    status: "Not Started",
    assignmentStatus: "Active",
    assignedDate: "2024-01-12",
    dueDate: "2024-02-12",
    notes: "CFD assignment for medication management documentation.",
  },
  {
    id: "6",
    employeeIds: ["7", "8"],
    employeeName: "2 Employees",
    trainingProgram:
      "Equipment Checklist → Maintenance Form → Maintenance Schedule Document | Equipment Checklist → Calibration Form → Calibration Procedure Document",
    businessUnit: "KHC",
    departmentGroup: "IT Services",
    department: "Healthcare IT",
    division: "Clinical Systems",
    subDivision: "EHR Implementation",
    category: "Technical",
    grade: "Specialist",
    designation: "IT Specialist",
    status: "Completed",
    assignmentStatus: "Active",
    assignedDate: "2024-01-01",
    dueDate: "2024-01-15",
    notes: "Completed CFD assignment for equipment maintenance documentation.",
  },
];

export default function ContentAssignmentViewCfd() {
  const navigate = useNavigate();
  
  // State for CFD area, topic, and unit dropdowns
  const [selectedCfdArea, setSelectedCfdArea] = useState("");
  const [selectedCfdTopic, setSelectedCfdTopic] = useState("");
  const [selectedCfdUnit, setSelectedCfdUnit] = useState("");

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          CFD Assignment Page
        </h1>
        <p className="text-muted-foreground">
          View and manage CFD assignment details with area, topic, and unit filtering
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          {/* CFD Topic Assignment Details Section */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
            <div className="px-4 py-3 bg-slate-100 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h4 className="font-semibold text-slate-800 flex items-center gap-2">
                    <svg
                      className="w-4 h-4 text-purple-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    CFD Topic Assignment Details
                  </h4>

                  {/* CFD Area Dropdown in Header */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="cfd-area" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Area:
                    </Label>
                    <Select
                      value={selectedCfdArea || ""}
                      onValueChange={(value) => {
                        setSelectedCfdArea(value);
                        if (!value) {
                          setSelectedCfdTopic(""); // Reset topic when clearing area
                          setSelectedCfdUnit(""); // Reset unit when clearing area
                        }
                      }}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select Area" />
                      </SelectTrigger>
                      <SelectContent>
                        {cfdAreas.map((area) => (
                          <SelectItem key={area} value={area}>
                            {area}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* CFD Topic Dropdown in Header */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="cfd-topic" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Topic:
                    </Label>
                    <Select
                      value={selectedCfdTopic || ""}
                      onValueChange={(value) => {
                        setSelectedCfdTopic(value);
                        if (!value) {
                          setSelectedCfdUnit(""); // Reset unit when clearing topic
                        }
                      }}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select Topic" />
                      </SelectTrigger>
                      <SelectContent>
                        {(() => {
                          // If area is selected, show only topics for that area
                          if (selectedCfdArea && selectedCfdArea !== "") {
                            const areaTopics = cfdTopics[selectedCfdArea as keyof typeof cfdTopics];
                            if (Array.isArray(areaTopics)) {
                              return areaTopics.map((topic: string) => (
                                <SelectItem key={topic} value={topic}>
                                  {topic}
                                </SelectItem>
                              ));
                            }
                            return [];
                          }

                          // If no area is selected, show all topics
                          const allTopics = Object.values(cfdTopics).flat();
                          const uniqueTopics = [...new Set(allTopics)];
                          return uniqueTopics.map((topic: string) => (
                            <SelectItem key={topic} value={topic}>
                              {topic}
                            </SelectItem>
                          ));
                        })()}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* CFD Unit Dropdown in Header */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="cfd-unit" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Unit:
                    </Label>
                    <Select
                      value={selectedCfdUnit || ""}
                      onValueChange={(value) => {
                        setSelectedCfdUnit(value);
                      }}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select Unit" />
                      </SelectTrigger>
                      <SelectContent>
                        {(() => {
                          // If topic is selected, show only units for that topic
                          if (selectedCfdTopic && selectedCfdTopic !== "") {
                            const topicUnits = cfdUnits[selectedCfdTopic as keyof typeof cfdUnits];
                            if (Array.isArray(topicUnits)) {
                              return topicUnits.map((unit: string) => (
                                <SelectItem key={unit} value={unit}>
                                  {unit}
                                </SelectItem>
                              ));
                            }
                            return [];
                          }

                          // If no topic is selected, show all units
                          const allUnits = Object.values(cfdUnits).flat();
                          const uniqueUnits = [...new Set(allUnits)];
                          return uniqueUnits.map((unit: string) => (
                            <SelectItem key={unit} value={unit}>
                              {unit}
                            </SelectItem>
                          ));
                        })()}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {/* Status Dropdown */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="status-filter" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Status:
                    </Label>
                    <Select
                      value="all"
                      onValueChange={(value) => {
                        // Handle status filter change if needed
                        console.log("Status filter changed:", value);
                      }}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        {statuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Export to Excel Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                    onClick={() => {
                      // Export filtered CFD assignment details to Excel
                      try {
                        // Get filtered data based on selected area, topic, and unit
                        let filteredAssignments = mockCfdAssignments;

                        if (selectedCfdArea || selectedCfdTopic || selectedCfdUnit) {
                          filteredAssignments = mockCfdAssignments.filter(assignment => {
                            // Extract areas, topics, and units from the training program
                            const programParts = assignment.trainingProgram
                              .split(" | ")
                              .map(program => program.split(" → "));

                            return programParts.some(parts => {
                              const [area, topic, unit] = parts;
                              const areaMatch = !selectedCfdArea || area === selectedCfdArea;
                              const topicMatch = !selectedCfdTopic || topic === selectedCfdTopic;
                              const unitMatch = !selectedCfdUnit || unit === selectedCfdUnit;
                              return areaMatch && topicMatch && unitMatch;
                            });
                          });
                        }

                        // Create export data
                        const exportData = filteredAssignments.flatMap(assignment => {
                          const assignedEmployees = assignment.employeeIds?.map((empId) => {
                            return `Employee ${empId}`;
                          }) || [assignment.employeeName || "Unknown Employee"];

                          return assignedEmployees.map(employeeName => ({
                            "Business Unit": assignment.businessUnit,
                            "Department Group": assignment.departmentGroup,
                            "Department": assignment.department,
                            "Division": assignment.division,
                            "Sub-Division": assignment.subDivision,
                            "Category": assignment.category,
                            "Grade": assignment.grade,
                            "Designation": assignment.designation,
                            "Assigned Employee": employeeName,
                            "Status": assignment.status,
                            "Training Program": assignment.trainingProgram
                          }));
                        });

                        // Export to Excel
                        const ws = XLSX.utils.json_to_sheet(exportData);
                        const wb = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(wb, ws, "CFD Assignment Details");
                        XLSX.writeFile(wb, `CFD_Assignment_Details_${new Date().toISOString().split('T')[0]}.xlsx`);

                        toast({
                          title: "Excel Generated",
                          description: "CFD assignment details have been exported to Excel.",
                        });
                      } catch (error) {
                        console.error("Error exporting to Excel:", error);
                        toast({
                          title: "Error",
                          description: "There was an error exporting to Excel. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <FileDown size={16} />
                    Export to Excel
                  </Button>
                </div>
              </div>
            </div>

            {/* CFD Assignment Details Table */}
            <div className="px-4 py-6">
              {/* Display filtered CFD assignments based on selected area, topic, and unit */}
              {(() => {
                // Filter CFD assignments based on selected area, topic, and unit
                let filteredCfdAssignments = mockCfdAssignments;

                if (selectedCfdArea || selectedCfdTopic || selectedCfdUnit) {
                  filteredCfdAssignments = mockCfdAssignments.filter(assignment => {
                    // Extract areas, topics, and units from the training program
                    const programParts = assignment.trainingProgram
                      .split(" | ")
                      .map(program => program.split(" → "));

                    return programParts.some(parts => {
                      const [area, topic, unit] = parts;
                      const areaMatch = !selectedCfdArea || area === selectedCfdArea;
                      const topicMatch = !selectedCfdTopic || topic === selectedCfdTopic;
                      const unitMatch = !selectedCfdUnit || unit === selectedCfdUnit;
                      return areaMatch && topicMatch && unitMatch;
                    });
                  });
                }

                if (filteredCfdAssignments.length === 0) {
                  return (
                    <div className="text-center py-8">
                      <div className="text-muted-foreground">
                        {selectedCfdArea || selectedCfdTopic || selectedCfdUnit
                          ? "No CFD assignments found for the selected criteria."
                          : "Please select an area, topic, and unit to view CFD assignment details."
                        }
                      </div>
                    </div>
                  );
                }

                // Generate table rows from filtered CFD assignments
                const generateCfdTableRows = () => {
                  const rows: any[] = [];

                  filteredCfdAssignments.forEach(assignment => {
                    // Parse business units, departments, etc.
                    const businessUnits = assignment.businessUnit.split(", ");
                    const departmentGroups = assignment.departmentGroup.split(", ");
                    const departments = assignment.department.split(", ");
                    const divisions = assignment.division.split(", ");
                    const subDivisions = assignment.subDivision.split(", ");
                    const categories = assignment.category.split(", ");
                    const grades = assignment.grade.split(", ");
                    const designations = assignment.designation.split(", ");

                    // Generate rows for all combinations
                    businessUnits.forEach((businessUnit) => {
                      departmentGroups.forEach((departmentGroup) => {
                        departments.forEach((department) => {
                          divisions.forEach((division) => {
                            subDivisions.forEach((subDivision) => {
                              categories.forEach((category) => {
                                grades.forEach((grade) => {
                                  designations.forEach((designation) => {
                                    // Generate static random employee count
                                    const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                                    let hash = 0;
                                    for (let i = 0; i < seed.length; i++) {
                                      const char = seed.charCodeAt(i);
                                      hash = ((hash << 5) - hash) + char;
                                      hash = hash & hash;
                                    }
                                    const employeeCount = Math.abs(hash % 15) + 1;

                                    const row = {
                                      businessUnit: businessUnit.trim(),
                                      departmentGroup: departmentGroup.trim(),
                                      department: department.trim(),
                                      division: division.trim(),
                                      subDivision: subDivision.trim(),
                                      category: category.trim(),
                                      grade: grade.trim(),
                                      designation: designation.trim(),
                                      employeeCount: employeeCount,
                                      assignedEmployee: `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`,
                                      status: assignment.status,
                                      dueDate: assignment.dueDate || "N/A",
                                    };
                                    rows.push(row);
                                  });
                                });
                              });
                            });
                          });
                        });
                      });
                    });
                  });

                  return rows;
                };

                const cfdTableRows = generateCfdTableRows();

                return (
                  <div className="space-y-4">
                    {/* CFD Assignment Details Table */}
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader className="bg-slate-50">
                          <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                            <TableHead className="font-medium text-left">Business Unit</TableHead>
                            <TableHead className="font-medium text-left">Department Group</TableHead>
                            <TableHead className="font-medium text-left">Department</TableHead>
                            <TableHead className="font-medium text-left">Division</TableHead>
                            <TableHead className="font-medium text-left">Sub-Division</TableHead>
                            <TableHead className="font-medium text-left">Category</TableHead>
                            <TableHead className="font-medium text-left">Grade</TableHead>
                            <TableHead className="font-medium text-left">Designation</TableHead>
                            <TableHead className="font-medium text-left">Assigned Employee</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {cfdTableRows.map((row, index) => (
                            <TableRow key={index} className="hover:bg-muted/50">
                              <TableCell className="font-medium">{row.businessUnit}</TableCell>
                              <TableCell>{row.departmentGroup}</TableCell>
                              <TableCell>{row.department}</TableCell>
                              <TableCell>{row.division}</TableCell>
                              <TableCell>{row.subDivision}</TableCell>
                              <TableCell>{row.category}</TableCell>
                              <TableCell>{row.grade}</TableCell>
                              <TableCell>{row.designation}</TableCell>
                              <TableCell>
                                <Button
                                  variant="link"
                                  className="p-0 h-auto text-purple-600 hover:text-purple-800"
                                  onClick={() => {
                                    // Handle employee count click - show user details popup
                                    toast({
                                      title: "Employee Details",
                                      description: `Showing details for ${row.assignedEmployee} in ${row.businessUnit}`,
                                    });
                                  }}
                                >
                                  {row.assignedEmployee}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
}
