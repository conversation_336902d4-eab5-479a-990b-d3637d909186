import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Filter,
  FileDown,
  ArrowLeft,
  X,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import * as XLSX from "xlsx";

// Training areas and topics (same as in EmployeeTrainingAssignment)
const trainingAreas = ["Clinical", "Administrative", "Technical"];

const trainingTopics = {
  Clinical: ["Patient Care", "Emergency Medicine", "Pharmacy", "Nursing"],
  Administrative: ["HR Management", "Finance", "Operations", "Quality Control"],
  Technical: [
    "IT Systems",
    "Medical Equipment",
    "Facilities",
    "Data Management",
  ],
};

const statuses = ["Not Started", "In Progress", "Completed"];

// Mock training assignments data (filtered for training only)
const mockTrainingAssignments = [
  {
    id: "1",
    employeeIds: ["1", "2"],
    employeeName: "2 Employees",
    trainingProgram:
      "Clinical → Patient Care → Basic Patient Care | Clinical → Nursing → Basic Nursing",
    businessUnit: "KCN, KTN",
    departmentGroup: "Clinical Services, Emergency Services",
    department: "Nursing, Intensive Care",
    division: "Patient Care, Specialized Nursing",
    subDivision: "Intensive Care, General Care",
    category: "Clinical, Critical Care",
    grade: "Junior, Mid-level",
    designation: "Nurse Assistant, Junior Technician",
    status: "Not Started",
    assignmentStatus: "Active",
    assignedDate: "2024-01-15",
    dueDate: "2024-02-15",
    notes: "Initial training assignment for new nursing staff.",
  },
  {
    id: "2",
    employeeIds: ["3", "4"],
    employeeName: "2 Employees",
    trainingProgram:
      "Administrative → HR Management → Recruitment | Administrative → Finance → Budgeting",
    businessUnit: "KHC",
    departmentGroup: "Management",
    department: "Hospital Administration",
    division: "Operations",
    subDivision: "Department Management",
    category: "Non-Clinical",
    grade: "Associate",
    designation: "Administrative Coordinator",
    status: "In Progress",
    assignmentStatus: "Active",
    assignedDate: "2024-01-10",
    dueDate: "2024-02-10",
    notes: "Administrative training for management staff.",
  },
  {
    id: "3",
    employeeIds: ["e1", "e2"],
    employeeName: "2 Employees",
    trainingProgram:
      "Technical → IT Systems → EHR Systems | Technical → Medical Equipment → Equipment Operation",
    businessUnit: "KCN",
    departmentGroup: "Clinical Services",
    department: "Surgery",
    division: "Internal Medicine",
    subDivision: "Cardiology",
    category: "Interventional Cardiology",
    grade: "Junior",
    designation: "Junior Technician",
    status: "Completed",
    assignmentStatus: "Active",
    assignedDate: "2024-01-05",
    dueDate: "2024-01-20",
    notes: "Completed technical training for IT systems and medical equipment.",
  },
];

export default function ContentAssignmentViewLearning() {
  const navigate = useNavigate();
  
  // State for assigned area and topic dropdowns
  const [selectedAssignedArea, setSelectedAssignedArea] = useState("");
  const [selectedAssignedTopic, setSelectedAssignedTopic] = useState("");

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          Learning Assignment Page
        </h1>
        <p className="text-muted-foreground">
          View and manage training assignment details with area and topic filtering
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          {/* Topic Assignment Details Section */}
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
            <div className="px-4 py-3 bg-slate-100 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h4 className="font-semibold text-slate-800 flex items-center gap-2">
                    <svg
                      className="w-4 h-4 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Topic Assignment Details
                  </h4>

                  {/* Assigned Area Dropdown in Header */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="assigned-area" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Assigned Area:
                    </Label>
                    <Select
                      value={selectedAssignedArea || ""}
                      onValueChange={(value) => {
                        setSelectedAssignedArea(value);
                        if (!value) {
                          setSelectedAssignedTopic(""); // Reset topic when clearing area
                        }
                      }}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select Area" />
                      </SelectTrigger>
                      <SelectContent>
                        {trainingAreas.map((area) => (
                          <SelectItem key={area} value={area}>
                            {area}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Assigned Topic Dropdown in Header */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="assigned-topic" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Topic:
                    </Label>
                    <Select
                      value={selectedAssignedTopic || ""}
                      onValueChange={(value) => {
                        setSelectedAssignedTopic(value);
                      }}
                    >
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Select Topic" />
                      </SelectTrigger>
                      <SelectContent>
                        {(() => {
                          // If area is selected, show only topics for that area
                          if (selectedAssignedArea && selectedAssignedArea !== "") {
                            const areaTopics = trainingTopics[selectedAssignedArea as keyof typeof trainingTopics];
                            if (Array.isArray(areaTopics)) {
                              return areaTopics.map((topic: string) => (
                                <SelectItem key={topic} value={topic}>
                                  {topic}
                                </SelectItem>
                              ));
                            }
                            return [];
                          }

                          // If no area is selected, show all topics
                          const allTopics = Object.values(trainingTopics).flat();
                          const uniqueTopics = [...new Set(allTopics)];
                          return uniqueTopics.map((topic: string) => (
                            <SelectItem key={topic} value={topic}>
                              {topic}
                            </SelectItem>
                          ));
                        })()}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {/* Status Dropdown */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="status-filter" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                      Status:
                    </Label>
                    <Select
                      value="all"
                      onValueChange={(value) => {
                        // Handle status filter change if needed
                        console.log("Status filter changed:", value);
                      }}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        {statuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Export to Excel Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                    onClick={() => {
                      // Export filtered assignment details to Excel
                      try {
                        // Get filtered data based on selected area and topic
                        let filteredAssignments = mockTrainingAssignments;

                        if (selectedAssignedArea || selectedAssignedTopic) {
                          filteredAssignments = mockTrainingAssignments.filter(assignment => {
                            // Extract areas and topics from the training program
                            const programParts = assignment.trainingProgram
                              .split(" | ")
                              .map(program => program.split(" → "));

                            return programParts.some(parts => {
                              const [area, topic] = parts;
                              const areaMatch = !selectedAssignedArea || area === selectedAssignedArea;
                              const topicMatch = !selectedAssignedTopic || topic === selectedAssignedTopic;
                              return areaMatch && topicMatch;
                            });
                          });
                        }

                        // Create export data
                        const exportData = filteredAssignments.flatMap(assignment => {
                          const assignedEmployees = assignment.employeeIds?.map((empId) => {
                            return `Employee ${empId}`;
                          }) || [assignment.employeeName || "Unknown Employee"];

                          return assignedEmployees.map(employeeName => ({
                            "Business Unit": assignment.businessUnit,
                            "Department Group": assignment.departmentGroup,
                            "Department": assignment.department,
                            "Division": assignment.division,
                            "Sub-Division": assignment.subDivision,
                            "Category": assignment.category,
                            "Grade": assignment.grade,
                            "Designation": assignment.designation,
                            "Assigned Employee": employeeName,
                            "Status": assignment.status,
                            "Training Program": assignment.trainingProgram
                          }));
                        });

                        // Export to Excel
                        const ws = XLSX.utils.json_to_sheet(exportData);
                        const wb = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(wb, ws, "Topic Assignment Details");
                        XLSX.writeFile(wb, `Topic_Assignment_Details_${new Date().toISOString().split('T')[0]}.xlsx`);

                        toast({
                          title: "Excel Generated",
                          description: "Topic assignment details have been exported to Excel.",
                        });
                      } catch (error) {
                        console.error("Error exporting to Excel:", error);
                        toast({
                          title: "Error",
                          description: "There was an error exporting to Excel. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <FileDown size={16} />
                    Export to Excel
                  </Button>
                </div>
              </div>
            </div>

            {/* Assignment Details Table */}
            <div className="px-4 py-6">
              {/* Display filtered assignments based on selected area and topic */}
              {(() => {
                // Filter assignments based on selected area and topic
                let filteredAssignments = mockTrainingAssignments;

                if (selectedAssignedArea || selectedAssignedTopic) {
                  filteredAssignments = mockTrainingAssignments.filter(assignment => {
                    // Extract areas and topics from the training program
                    const programParts = assignment.trainingProgram
                      .split(" | ")
                      .map(program => program.split(" → "));

                    return programParts.some(parts => {
                      const [area, topic] = parts;
                      const areaMatch = !selectedAssignedArea || area === selectedAssignedArea;
                      const topicMatch = !selectedAssignedTopic || topic === selectedAssignedTopic;
                      return areaMatch && topicMatch;
                    });
                  });
                }

                if (filteredAssignments.length === 0) {
                  return (
                    <div className="text-center py-8">
                      <div className="text-muted-foreground">
                        {selectedAssignedArea || selectedAssignedTopic
                          ? "No assignments found for the selected area and topic."
                          : "Please select an area and topic to view assignment details."
                        }
                      </div>
                    </div>
                  );
                }

                // Generate table rows from filtered assignments
                const generateTableRows = () => {
                  const rows: any[] = [];

                  filteredAssignments.forEach(assignment => {
                    // Parse business units, departments, etc.
                    const businessUnits = assignment.businessUnit.split(", ");
                    const departmentGroups = assignment.departmentGroup.split(", ");
                    const departments = assignment.department.split(", ");
                    const divisions = assignment.division.split(", ");
                    const subDivisions = assignment.subDivision.split(", ");
                    const categories = assignment.category.split(", ");
                    const grades = assignment.grade.split(", ");
                    const designations = assignment.designation.split(", ");

                    // Generate rows for all combinations
                    businessUnits.forEach((businessUnit) => {
                      departmentGroups.forEach((departmentGroup) => {
                        departments.forEach((department) => {
                          divisions.forEach((division) => {
                            subDivisions.forEach((subDivision) => {
                              categories.forEach((category) => {
                                grades.forEach((grade) => {
                                  designations.forEach((designation) => {
                                    // Generate static random employee count
                                    const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                                    let hash = 0;
                                    for (let i = 0; i < seed.length; i++) {
                                      const char = seed.charCodeAt(i);
                                      hash = ((hash << 5) - hash) + char;
                                      hash = hash & hash;
                                    }
                                    const employeeCount = Math.abs(hash % 15) + 1;

                                    const row = {
                                      businessUnit: businessUnit.trim(),
                                      departmentGroup: departmentGroup.trim(),
                                      department: department.trim(),
                                      division: division.trim(),
                                      subDivision: subDivision.trim(),
                                      category: category.trim(),
                                      grade: grade.trim(),
                                      designation: designation.trim(),
                                      employeeCount: employeeCount,
                                      assignedEmployee: `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`,
                                      status: assignment.status,
                                      dueDate: assignment.dueDate || "N/A",
                                    };
                                    rows.push(row);
                                  });
                                });
                              });
                            });
                          });
                        });
                      });
                    });
                  });

                  return rows;
                };

                const tableRows = generateTableRows();

                return (
                  <div className="space-y-4">
                    {/* Assignment Details Table */}
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader className="bg-slate-50">
                          <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                            <TableHead className="font-medium text-left">Business Unit</TableHead>
                            <TableHead className="font-medium text-left">Department Group</TableHead>
                            <TableHead className="font-medium text-left">Department</TableHead>
                            <TableHead className="font-medium text-left">Division</TableHead>
                            <TableHead className="font-medium text-left">Sub-Division</TableHead>
                            <TableHead className="font-medium text-left">Category</TableHead>
                            <TableHead className="font-medium text-left">Grade</TableHead>
                            <TableHead className="font-medium text-left">Designation</TableHead>
                            <TableHead className="font-medium text-left">Assigned Employee</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {tableRows.map((row, index) => (
                            <TableRow key={index} className="hover:bg-muted/50">
                              <TableCell className="font-medium">{row.businessUnit}</TableCell>
                              <TableCell>{row.departmentGroup}</TableCell>
                              <TableCell>{row.department}</TableCell>
                              <TableCell>{row.division}</TableCell>
                              <TableCell>{row.subDivision}</TableCell>
                              <TableCell>{row.category}</TableCell>
                              <TableCell>{row.grade}</TableCell>
                              <TableCell>{row.designation}</TableCell>
                              <TableCell>
                                <Button
                                  variant="link"
                                  className="p-0 h-auto text-blue-600 hover:text-blue-800"
                                  onClick={() => {
                                    // Handle employee count click - show user details popup
                                    toast({
                                      title: "Employee Details",
                                      description: `Showing details for ${row.assignedEmployee} in ${row.businessUnit}`,
                                    });
                                  }}
                                >
                                  {row.assignedEmployee}
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
}
